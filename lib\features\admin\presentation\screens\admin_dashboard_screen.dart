import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/product_provider.dart';
import '../../../../shared/providers/dynamic_content_provider.dart';

class AdminDashboardScreen extends ConsumerStatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  ConsumerState<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends ConsumerState<AdminDashboardScreen> {
  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider).value;
    
    // Check if user is admin (you can implement your own admin check logic)
    if (currentUser == null || !_isAdmin(currentUser.email)) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Access Denied'),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock,
                size: 64,
                color: AppTheme.errorColor,
              ),
              SizedBox(height: 16),
              Text(
                'Admin access required',
                style: AppTextStyles.heading2,
              ),
              SizedBox(height: 8),
              Text(
                'You do not have permission to access this area.',
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        title: const Text('Admin Dashboard'),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryGreen,
                    AppTheme.primaryGreen.withOpacity(0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome, ${currentUser.name}',
                    style: AppTextStyles.heading2.copyWith(
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Manage your EcoCura platform',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Quick Stats
            Text(
              'Quick Stats',
              style: AppTextStyles.heading3.copyWith(
                color: AppTheme.darkGreen,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildStatsGrid(),
            
            const SizedBox(height: 32),
            
            // Management Sections
            Text(
              'Management',
              style: AppTextStyles.heading3.copyWith(
                color: AppTheme.darkGreen,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildManagementGrid(),
            
            const SizedBox(height: 32),
            
            // System Tools
            Text(
              'System Tools',
              style: AppTextStyles.heading3.copyWith(
                color: AppTheme.darkGreen,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildSystemToolsGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          title: 'Total Products',
          value: '156',
          icon: Icons.inventory,
          color: AppTheme.primaryGreen,
        ),
        _buildStatCard(
          title: 'Active Users',
          value: '1,234',
          icon: Icons.people,
          color: Colors.blue,
        ),
        _buildStatCard(
          title: 'Total Orders',
          value: '89',
          icon: Icons.shopping_cart,
          color: Colors.orange,
        ),
        _buildStatCard(
          title: 'Revenue',
          value: '₹45,678',
          icon: Icons.attach_money,
          color: Colors.green,
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
            ],
          ),
          const Spacer(),
          Text(
            value,
            style: AppTextStyles.heading2.copyWith(
              color: AppTheme.darkGreen,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagementGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildManagementCard(
          title: 'Products',
          subtitle: 'Manage inventory',
          icon: Icons.inventory,
          onTap: () => _navigateToProductManagement(),
        ),
        _buildManagementCard(
          title: 'Users',
          subtitle: 'User accounts',
          icon: Icons.people,
          onTap: () => _navigateToUserManagement(),
        ),
        _buildManagementCard(
          title: 'Orders',
          subtitle: 'Order tracking',
          icon: Icons.shopping_cart,
          onTap: () => _navigateToOrderManagement(),
        ),
        _buildManagementCard(
          title: 'Categories',
          subtitle: 'Product categories',
          icon: Icons.category,
          onTap: () => _navigateToCategoryManagement(),
        ),
      ],
    );
  }

  Widget _buildManagementCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.lightGreen.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: AppTheme.primaryGreen,
                size: 24,
              ),
            ),
            const Spacer(),
            Text(
              title,
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.darkGreen,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemToolsGrid() {
    return Column(
      children: [
        _buildSystemToolCard(
          title: 'Initialize Schema',
          subtitle: 'Set up Firestore collections',
          icon: Icons.storage,
          onTap: () => _initializeSchema(),
        ),
        const SizedBox(height: 12),
        _buildSystemToolCard(
          title: 'Clear Cache',
          subtitle: 'Clear application cache',
          icon: Icons.clear_all,
          onTap: () => _clearCache(),
        ),
        const SizedBox(height: 12),
        _buildSystemToolCard(
          title: 'Export Data',
          subtitle: 'Download platform data',
          icon: Icons.download,
          onTap: () => _exportData(),
        ),
      ],
    );
  }

  Widget _buildSystemToolCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.lightGreen.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: AppTheme.primaryGreen,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.darkGreen,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppTheme.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  bool _isAdmin(String email) {
    // Implement your admin check logic here
    // For demo purposes, we'll check for specific email domains
    return email.endsWith('@ecocura.com') || 
           email.endsWith('@admin.com') ||
           email == '<EMAIL>';
  }

  void _navigateToProductManagement() {
    // TODO: Navigate to product management screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Product management coming soon!')),
    );
  }

  void _navigateToUserManagement() {
    // TODO: Navigate to user management screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('User management coming soon!')),
    );
  }

  void _navigateToOrderManagement() {
    // TODO: Navigate to order management screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Order management coming soon!')),
    );
  }

  void _navigateToCategoryManagement() {
    // TODO: Navigate to category management screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Category management coming soon!')),
    );
  }

  Future<void> _initializeSchema() async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Initializing schema...'),
            ],
          ),
        ),
      );

      // Initialize schema
      await FirestoreSchemaService.initializeSchema();

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Schema initialized successfully!'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error initializing schema: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _clearCache() {
    final cacheManager = ref.read(cacheManagerProvider);
    cacheManager.clear();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Cache cleared successfully!'),
        backgroundColor: AppTheme.primaryGreen,
      ),
    );
  }

  void _exportData() {
    // TODO: Implement data export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Data export coming soon!')),
    );
  }
}
