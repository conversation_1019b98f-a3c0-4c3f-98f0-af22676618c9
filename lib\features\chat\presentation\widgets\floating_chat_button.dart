import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../screens/chat_screen.dart';
import '../../providers/chat_provider.dart';

class FloatingChatButton extends ConsumerStatefulWidget {
  const FloatingChatButton({super.key});

  @override
  ConsumerState<FloatingChatButton> createState() => _FloatingChatButtonState();
}

class _FloatingChatButtonState extends ConsumerState<FloatingChatButton>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start entrance animation
    _animationController.forward();
    
    // Start pulse animation
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  void _openChat() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ChatScreen(),
        fullscreenDialog: true,
      ),
    );
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildQuickActionsSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final unreadCount = ref.watch(unreadMessagesCountProvider);

    return Positioned(
      bottom: 20,
      right: 20,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Quick Actions (when expanded)
                if (_isExpanded) ...[
                  _buildQuickActionButton(
                    icon: Icons.lightbulb_outline,
                    label: 'Get Ideas',
                    onTap: () => _handleQuickAction('ideas'),
                  ),
                  const SizedBox(height: 12),
                  _buildQuickActionButton(
                    icon: Icons.help_outline,
                    label: 'How To',
                    onTap: () => _handleQuickAction('howto'),
                  ),
                  const SizedBox(height: 12),
                  _buildQuickActionButton(
                    icon: Icons.search,
                    label: 'Find Products',
                    onTap: () => _handleQuickAction('search'),
                  ),
                  const SizedBox(height: 16),
                ],
                
                // Main Chat Button
                GestureDetector(
                  onTap: _isExpanded ? _openChat : _toggleExpanded,
                  onLongPress: _showQuickActions,
                  child: AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _isExpanded ? 1.0 : _pulseAnimation.value,
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppTheme.primaryGreen,
                                AppTheme.primaryGreen.withOpacity(0.8),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.primaryGreen.withOpacity(0.3),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Stack(
                            children: [
                              Center(
                                child: AnimatedSwitcher(
                                  duration: const Duration(milliseconds: 200),
                                  child: Icon(
                                    _isExpanded ? Icons.chat : Icons.smart_toy,
                                    key: ValueKey(_isExpanded),
                                    color: Colors.white,
                                    size: 28,
                                  ),
                                ),
                              ),
                              
                              // Unread badge
                              if (unreadCount > 0)
                                Positioned(
                                  top: 8,
                                  right: 8,
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: const BoxDecoration(
                                      color: AppTheme.errorColor,
                                      shape: BoxShape.circle,
                                    ),
                                    constraints: const BoxConstraints(
                                      minWidth: 16,
                                      minHeight: 16,
                                    ),
                                    child: Text(
                                      unreadCount > 9 ? '9+' : unreadCount.toString(),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                
                // Close button (when expanded)
                if (_isExpanded) ...[
                  const SizedBox(height: 12),
                  GestureDetector(
                    onTap: _toggleExpanded,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade600,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: AppTheme.primaryGreen,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppTheme.primaryGreen,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSheet() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Text(
                  'How can I help you?',
                  style: AppTextStyles.heading3.copyWith(
                    color: AppTheme.darkGreen,
                  ),
                ),
                const SizedBox(height: 20),
                
                _buildQuickActionTile(
                  icon: Icons.lightbulb_outline,
                  title: 'Get Upcycling Ideas',
                  subtitle: 'Discover creative projects for your materials',
                  onTap: () => _handleQuickAction('ideas'),
                ),
                
                _buildQuickActionTile(
                  icon: Icons.help_outline,
                  title: 'How-to Guides',
                  subtitle: 'Step-by-step instructions for projects',
                  onTap: () => _handleQuickAction('howto'),
                ),
                
                _buildQuickActionTile(
                  icon: Icons.search,
                  title: 'Find Products',
                  subtitle: 'Search for eco-friendly items',
                  onTap: () => _handleQuickAction('search'),
                ),
                
                _buildQuickActionTile(
                  icon: Icons.camera_alt,
                  title: 'Identify Materials',
                  subtitle: 'Take a photo to identify waste materials',
                  onTap: () => _handleQuickAction('identify'),
                ),
                
                const SizedBox(height: 10),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.lightGreen.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          icon,
          color: AppTheme.primaryGreen,
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyLarge.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppTheme.textSecondary,
        ),
      ),
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
    );
  }

  void _handleQuickAction(String action) {
    final chatNotifier = ref.read(chatProvider.notifier);
    
    switch (action) {
      case 'ideas':
        chatNotifier.sendQuickAction('Show me creative upcycling ideas for common household items');
        break;
      case 'howto':
        chatNotifier.sendQuickAction('I need step-by-step instructions for an upcycling project');
        break;
      case 'search':
        chatNotifier.sendQuickAction('Help me find eco-friendly products in the marketplace');
        break;
      case 'identify':
        chatNotifier.sendQuickAction('I want to identify materials from a photo for upcycling');
        break;
    }
    
    _openChat();
  }
}
