import 'package:flutter/foundation.dart';

/// Represents a 3D object in AR space
class ARObject {
  final String id;
  final String name;
  final String modelUrl;
  final ARVector3 position;
  final ARVector3 rotation;
  final ARVector3 scale;
  final ARMaterial? material;
  final Map<String, dynamic>? metadata;
  final bool isVisible;
  final bool isInteractable;

  ARObject({
    required this.id,
    required this.name,
    required this.modelUrl,
    this.position = const ARVector3(0, 0, 0),
    this.rotation = const ARVector3(0, 0, 0),
    this.scale = const ARVector3(1, 1, 1),
    this.material,
    this.metadata,
    this.isVisible = true,
    this.isInteractable = true,
  });

  /// Create a copy of this object with modified properties
  ARObject copyWith({
    String? id,
    String? name,
    String? modelUrl,
    ARVector3? position,
    ARVector3? rotation,
    ARVector3? scale,
    ARMaterial? material,
    Map<String, dynamic>? metadata,
    bool? isVisible,
    bool? isInteractable,
  }) {
    return ARObject(
      id: id ?? this.id,
      name: name ?? this.name,
      modelUrl: modelUrl ?? this.modelUrl,
      position: position ?? this.position,
      rotation: rotation ?? this.rotation,
      scale: scale ?? this.scale,
      material: material ?? this.material,
      metadata: metadata ?? this.metadata,
      isVisible: isVisible ?? this.isVisible,
      isInteractable: isInteractable ?? this.isInteractable,
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'modelUrl': modelUrl,
      'position': position.toJson(),
      'rotation': rotation.toJson(),
      'scale': scale.toJson(),
      'material': material?.toJson(),
      'metadata': metadata,
      'isVisible': isVisible,
      'isInteractable': isInteractable,
    };
  }

  /// Create from JSON
  factory ARObject.fromJson(Map<String, dynamic> json) {
    return ARObject(
      id: json['id'],
      name: json['name'],
      modelUrl: json['modelUrl'],
      position: ARVector3.fromJson(json['position']),
      rotation: ARVector3.fromJson(json['rotation']),
      scale: ARVector3.fromJson(json['scale']),
      material: json['material'] != null 
          ? ARMaterial.fromJson(json['material']) 
          : null,
      metadata: json['metadata'],
      isVisible: json['isVisible'] ?? true,
      isInteractable: json['isInteractable'] ?? true,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ARObject && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ARObject(id: $id, name: $name, position: $position)';
  }
}

/// 3D Vector class for positions, rotations, and scales
class ARVector3 {
  final double x;
  final double y;
  final double z;

  const ARVector3(this.x, this.y, this.z);

  /// Zero vector
  static const ARVector3 zero = ARVector3(0, 0, 0);
  
  /// Unit vector
  static const ARVector3 one = ARVector3(1, 1, 1);
  
  /// Forward vector
  static const ARVector3 forward = ARVector3(0, 0, 1);
  
  /// Up vector
  static const ARVector3 up = ARVector3(0, 1, 0);
  
  /// Right vector
  static const ARVector3 right = ARVector3(1, 0, 0);

  /// Add two vectors
  ARVector3 operator +(ARVector3 other) {
    return ARVector3(x + other.x, y + other.y, z + other.z);
  }

  /// Subtract two vectors
  ARVector3 operator -(ARVector3 other) {
    return ARVector3(x - other.x, y - other.y, z - other.z);
  }

  /// Multiply vector by scalar
  ARVector3 operator *(double scalar) {
    return ARVector3(x * scalar, y * scalar, z * scalar);
  }

  /// Divide vector by scalar
  ARVector3 operator /(double scalar) {
    return ARVector3(x / scalar, y / scalar, z / scalar);
  }

  /// Calculate magnitude of vector
  double get magnitude => sqrt(x * x + y * y + z * z);

  /// Get normalized vector
  ARVector3 get normalized {
    final mag = magnitude;
    if (mag == 0) return ARVector3.zero;
    return this / mag;
  }

  /// Calculate distance to another vector
  double distanceTo(ARVector3 other) {
    return (this - other).magnitude;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {'x': x, 'y': y, 'z': z};
  }

  /// Create from JSON
  factory ARVector3.fromJson(Map<String, dynamic> json) {
    return ARVector3(
      (json['x'] ?? 0).toDouble(),
      (json['y'] ?? 0).toDouble(),
      (json['z'] ?? 0).toDouble(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ARVector3 && 
           other.x == x && 
           other.y == y && 
           other.z == z;
  }

  @override
  int get hashCode => Object.hash(x, y, z);

  @override
  String toString() => 'ARVector3($x, $y, $z)';
}

/// Material properties for AR objects
class ARMaterial {
  final String? diffuseTexture;
  final String? normalTexture;
  final String? metallicRoughnessTexture;
  final ARColor diffuseColor;
  final double metallic;
  final double roughness;
  final double opacity;

  ARMaterial({
    this.diffuseTexture,
    this.normalTexture,
    this.metallicRoughnessTexture,
    this.diffuseColor = const ARColor(1.0, 1.0, 1.0, 1.0),
    this.metallic = 0.0,
    this.roughness = 0.5,
    this.opacity = 1.0,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'diffuseTexture': diffuseTexture,
      'normalTexture': normalTexture,
      'metallicRoughnessTexture': metallicRoughnessTexture,
      'diffuseColor': diffuseColor.toJson(),
      'metallic': metallic,
      'roughness': roughness,
      'opacity': opacity,
    };
  }

  /// Create from JSON
  factory ARMaterial.fromJson(Map<String, dynamic> json) {
    return ARMaterial(
      diffuseTexture: json['diffuseTexture'],
      normalTexture: json['normalTexture'],
      metallicRoughnessTexture: json['metallicRoughnessTexture'],
      diffuseColor: json['diffuseColor'] != null 
          ? ARColor.fromJson(json['diffuseColor'])
          : const ARColor(1.0, 1.0, 1.0, 1.0),
      metallic: (json['metallic'] ?? 0.0).toDouble(),
      roughness: (json['roughness'] ?? 0.5).toDouble(),
      opacity: (json['opacity'] ?? 1.0).toDouble(),
    );
  }
}

/// Color class for AR materials
class ARColor {
  final double r;
  final double g;
  final double b;
  final double a;

  const ARColor(this.r, this.g, this.b, this.a);

  /// Common colors
  static const ARColor white = ARColor(1.0, 1.0, 1.0, 1.0);
  static const ARColor black = ARColor(0.0, 0.0, 0.0, 1.0);
  static const ARColor red = ARColor(1.0, 0.0, 0.0, 1.0);
  static const ARColor green = ARColor(0.0, 1.0, 0.0, 1.0);
  static const ARColor blue = ARColor(0.0, 0.0, 1.0, 1.0);

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {'r': r, 'g': g, 'b': b, 'a': a};
  }

  /// Create from JSON
  factory ARColor.fromJson(Map<String, dynamic> json) {
    return ARColor(
      (json['r'] ?? 1.0).toDouble(),
      (json['g'] ?? 1.0).toDouble(),
      (json['b'] ?? 1.0).toDouble(),
      (json['a'] ?? 1.0).toDouble(),
    );
  }

  @override
  String toString() => 'ARColor($r, $g, $b, $a)';
}

/// Predefined AR objects for common use cases
class ARObjectTemplates {
  /// Create a product preview object
  static ARObject createProductPreview({
    required String productId,
    required String modelUrl,
    ARVector3? position,
    ARVector3? scale,
  }) {
    return ARObject(
      id: 'product_$productId',
      name: 'Product Preview',
      modelUrl: modelUrl,
      position: position ?? ARVector3.zero,
      scale: scale ?? ARVector3.one,
      metadata: {
        'type': 'product_preview',
        'productId': productId,
      },
    );
  }

  /// Create an upcycling project step object
  static ARObject createProjectStep({
    required String projectId,
    required int stepNumber,
    required String modelUrl,
    ARVector3? position,
  }) {
    return ARObject(
      id: 'project_${projectId}_step_$stepNumber',
      name: 'Project Step $stepNumber',
      modelUrl: modelUrl,
      position: position ?? ARVector3.zero,
      metadata: {
        'type': 'project_step',
        'projectId': projectId,
        'stepNumber': stepNumber,
      },
    );
  }

  /// Create a material identification object
  static ARObject createMaterialMarker({
    required String materialType,
    required String modelUrl,
    ARVector3? position,
  }) {
    return ARObject(
      id: 'material_$materialType',
      name: 'Material: $materialType',
      modelUrl: modelUrl,
      position: position ?? ARVector3.zero,
      scale: const ARVector3(0.5, 0.5, 0.5),
      metadata: {
        'type': 'material_marker',
        'materialType': materialType,
      },
    );
  }
}

// Helper function for square root (since dart:math is not imported)
double sqrt(double x) {
  // Simple approximation for square root
  if (x < 0) return double.nan;
  if (x == 0) return 0;
  
  double guess = x / 2;
  for (int i = 0; i < 10; i++) {
    guess = (guess + x / guess) / 2;
  }
  return guess;
}
