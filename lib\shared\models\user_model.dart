import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String name;
  final String email;
  final String? profileImageUrl;
  final UserTier tier;
  final int points;
  final int pointsToNextTier;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;
  final bool isStoreOwner;
  final String? storeId;
  final bool isEmailVerified;
  final String? phoneNumber;
  final String? address;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.profileImageUrl,
    required this.tier,
    required this.points,
    required this.pointsToNextTier,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
    this.isStoreOwner = false,
    this.storeId,
    this.isEmailVerified = false,
    this.phoneNumber,
    this.address,
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      id: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      profileImageUrl: data['profileImageUrl'],
      tier: UserTier.values.firstWhere(
        (t) => t.name == data['tier'],
        orElse: () => UserTier.bronze,
      ),
      points: data['points'] ?? 0,
      pointsToNextTier: data['pointsToNextTier'] ?? 0,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      lastLoginAt: data['lastLoginAt'] != null
          ? (data['lastLoginAt'] as Timestamp).toDate()
          : null,
      isStoreOwner: data['isStoreOwner'] ?? false,
      storeId: data['storeId'],
      isEmailVerified: data['isEmailVerified'] ?? false,
      phoneNumber: data['phoneNumber'],
      address: data['address'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'email': email,
      'profileImageUrl': profileImageUrl,
      'tier': tier.name,
      'points': points,
      'pointsToNextTier': pointsToNextTier,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'lastLoginAt': lastLoginAt != null ? Timestamp.fromDate(lastLoginAt!) : null,
      'isStoreOwner': isStoreOwner,
      'storeId': storeId,
      'isEmailVerified': isEmailVerified,
      'phoneNumber': phoneNumber,
      'address': address,
    };
  }

  UserModel copyWith({
    String? name,
    String? email,
    String? profileImageUrl,
    UserTier? tier,
    int? points,
    int? pointsToNextTier,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    bool? isStoreOwner,
    String? storeId,
    bool? isEmailVerified,
    String? phoneNumber,
    String? address,
  }) {
    return UserModel(
      id: id,
      name: name ?? this.name,
      email: email ?? this.email,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      tier: tier ?? this.tier,
      points: points ?? this.points,
      pointsToNextTier: pointsToNextTier ?? this.pointsToNextTier,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isStoreOwner: isStoreOwner ?? this.isStoreOwner,
      storeId: storeId ?? this.storeId,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,
    );
  }
}

enum UserTier {
  bronze,
  silver,
  gold;

  String get displayName {
    switch (this) {
      case UserTier.bronze:
        return 'Bronze';
      case UserTier.silver:
        return 'Silver';
      case UserTier.gold:
        return 'Gold';
    }
  }

  String get description {
    switch (this) {
      case UserTier.bronze:
        return 'Earning 5 points per 100rs';
      case UserTier.silver:
        return 'Earning 7 points per 100rs';
      case UserTier.gold:
        return 'Earning 10 points per 100rs';
    }
  }

  String get iconAsset {
    switch (this) {
      case UserTier.bronze:
        return 'assets/images/bronze-medal.png';
      case UserTier.silver:
        return 'assets/images/silver-medal.png';
      case UserTier.gold:
        return 'assets/images/gold-medal.png';
    }
  }

  int get pointsRequired {
    switch (this) {
      case UserTier.bronze:
        return 0;
      case UserTier.silver:
        return 500;
      case UserTier.gold:
        return 1500;
    }
  }
}
