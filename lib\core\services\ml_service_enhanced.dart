import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:google_generative_ai/google_generative_ai.dart';

class MLService {
  static GenerativeModel? _geminiModel;
  static List<String>? _labels;
  static bool _isInitialized = false;
  static final Random _random = Random();

  // Gemini API configuration
  static const String _apiKey = 'AIzaSyAV5MKNN0-WmFl-5AGU_vAAsGW34bpBJ5A';
  static const String _modelName = 'gemini-2.0-flash';

  // Initialize the ML model with Gemini AI
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load labels
      final labelsData = await rootBundle.loadString('assets/ml_models/labels.txt');
      _labels = labelsData.split('\n').where((label) => label.isNotEmpty).toList();

      // Initialize Gemini model
      try {
        _geminiModel = GenerativeModel(
          model: _modelName,
          apiKey: _apiKey,
        );
        if (kDebugMode) {
          print('✅ Gemini AI model initialized successfully!');
          print('🤖 Model: $_modelName');
          print('🏷️ Available labels: $_labels');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Failed to initialize Gemini model: $e');
          print('Falling back to mock data');
        }
        _geminiModel = null;
      }

      _isInitialized = true;
      if (kDebugMode) {
        print('ML Service initialized - ${_geminiModel != null ? "Gemini AI" : "Mock data fallback"}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to initialize ML Service: $e');
      }
      _isInitialized = false;
    }
  }

  // Classify waste from image bytes (web-compatible with mock data)
  static Future<WasteDetectionResult> classifyWaste(Uint8List imageBytes) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (kIsWeb) {
      // Web platform: TensorFlow Lite limitations explained
      if (kDebugMode) {
        print('=== TensorFlow Lite Web Limitations ===');
        print('❌ TensorFlow Lite does NOT work in web browsers because:');
        print('1. TensorFlow Lite is designed for mobile/embedded devices');
        print('2. Web browsers cannot access native TensorFlow Lite libraries');
        print('3. JavaScript/WebAssembly has different runtime requirements');
        print('4. Alternative: Use TensorFlow.js for web-based ML inference');
        print('5. Current solution: Mock data for web compatibility');
        print('Using mock ML results for web platform');
      }
      return _getMockResult();
    } else {
      // Mobile platform: Use Gemini AI if available, otherwise fallback to mock
      if (_geminiModel != null) {
        try {
          return await _runGeminiInference(imageBytes);
        } catch (e) {
          if (kDebugMode) {
            print('❌ Gemini inference failed: $e');
            print('Falling back to mock data');
          }
          return _getMockResult();
        }
      } else {
        if (kDebugMode) {
          print('=== Mobile Platform ML Status ===');
          print('🤖 Gemini AI not available - using mock data');
          print('📁 Check API key and internet connection');
        }
        return _getMockResult();
      }
    }
  }

  // Run Gemini AI inference
  static Future<WasteDetectionResult> _runGeminiInference(Uint8List imageBytes) async {
    if (_geminiModel == null || _labels == null) {
      throw Exception('Gemini model or labels not loaded');
    }

    try {
      // Create the prompt for waste classification
      final availableLabels = _labels!.map((label) {
        // Clean labels (remove number prefixes)
        if (label.contains(' ')) {
          final parts = label.split(' ');
          if (parts.length > 1 && RegExp(r'^\d+$').hasMatch(parts[0])) {
            return parts.sublist(1).join(' ');
          }
        }
        return label;
      }).toList();

      final prompt = '''
Analyze this image and identify the waste/recyclable item.
You must respond with ONLY ONE of these exact labels: ${availableLabels.join(', ')}

Look at the image carefully and determine which category the main object belongs to.
Respond with just the category name, nothing else.

If you see a plastic bottle, respond: Bottle
If you see cardboard, respond: Cardboard
If you see a tin can, respond: tincan
If you see wood, respond: wood

Be precise and use only the exact labels provided.
''';

      // Create content with image
      final content = [
        Content.multi([
          TextPart(prompt),
          DataPart('image/jpeg', imageBytes),
        ])
      ];

      if (kDebugMode) {
        print('🤖 Sending image to Gemini AI...');
        print('📝 Available labels: $availableLabels');
      }

      // Generate response
      final response = await _geminiModel!.generateContent(content);
      final responseText = response.text?.trim() ?? '';

      if (kDebugMode) {
        print('🤖 Gemini raw response: "$responseText"');
      }

      // Find the best matching label
      String detectedLabel = 'Unknown';
      double confidence = 0.0;

      for (final label in availableLabels) {
        if (responseText.toLowerCase().contains(label.toLowerCase())) {
          detectedLabel = label;
          confidence = 0.95; // High confidence for Gemini
          break;
        }
      }

      // If no exact match, try partial matching
      if (detectedLabel == 'Unknown') {
        for (final label in availableLabels) {
          final words = label.toLowerCase().split(' ');
          for (final word in words) {
            if (responseText.toLowerCase().contains(word)) {
              detectedLabel = label;
              confidence = 0.85; // Lower confidence for partial match
              break;
            }
          }
          if (detectedLabel != 'Unknown') break;
        }
      }

      // Fallback to first available label if still unknown
      if (detectedLabel == 'Unknown') {
        detectedLabel = availableLabels.first;
        confidence = 0.60; // Low confidence for fallback
      }

      if (kDebugMode) {
        print('=== Gemini AI Results ===');
        print('🎯 Detected: $detectedLabel');
        print('📊 Confidence: ${(confidence * 100).toStringAsFixed(1)}%');
        print('🤖 Raw response: "$responseText"');
      }

      return WasteDetectionResult(
        label: detectedLabel,
        confidence: confidence,
        suggestions: _getUpcyclingSuggestions(detectedLabel.toLowerCase()),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in Gemini inference: $e');
      }
      rethrow;
    }
  }

  // Get upcycling suggestions based on detected waste type
  static List<String> _getUpcyclingSuggestions(String wasteType) {
    final suggestions = {
      'plastic bottles': [
        'Bird Feeder - Cut holes and add perches for birds',
        'Planter Pot - Perfect container for herbs and small plants',
        'Pen Holder - Organize desk supplies and stationery',
      ],
      'wood': [
        'Bookshelf - Create simple shelving for storage',
        'Picture Frame - Make custom frames for photos',
        'Garden Planter Box - Build raised beds for plants',
      ],
      'cardboard': [
        'Storage Organizer - Multiple compartments for items',
        'Cat House - Fun shelter project for pets',
        'Desk Organizer - Sort papers and office supplies',
      ],
      'tin cans': [
        'Lantern - Add holes for beautiful light patterns',
        'Pencil Holder - Wrap with decorative paper',
        'Succulent Planter - Perfect size for small plants',
      ],
    };

    return suggestions[wasteType.toLowerCase()] ?? [
      'Creative Decoration - Paint and personalize',
      'Storage Solution - Organize small items',
      'Garden Helper - Use in outdoor projects',
    ];
  }

  // Mock result for development/fallback
  static WasteDetectionResult _getMockResult() {
    // Use the loaded labels if available, otherwise use default
    final availableLabels = _labels ?? ['Plastic Bottles', 'Wood', 'Cardboard', 'Tin cans'];
    final randomLabel = availableLabels[_random.nextInt(availableLabels.length)];

    // DEBUG LOGGING: ML Service behavior
    if (kDebugMode) {
      print('=== ML Service Debug ===');
      print('Using mock result (TensorFlow Lite disabled)');
      print('Available labels: $availableLabels');
      print('Selected label: $randomLabel');
      print('Platform: ${kIsWeb ? "Web" : "Mobile"}');
      print('TensorFlow Lite support: ${kIsWeb ? "Not supported" : "Available but disabled"}');
    }

    // Clean the label to remove count prefixes (addressing "0 bottle", "2 tincan" issue)
    String cleanLabel = randomLabel;
    if (cleanLabel.contains(' ')) {
      final parts = cleanLabel.split(' ');
      if (parts.length > 1 && RegExp(r'^\d+$').hasMatch(parts[0])) {
        cleanLabel = parts.sublist(1).join(' ');
        if (kDebugMode) {
          print('Cleaned label from "$randomLabel" to "$cleanLabel"');
        }
      }
    }

    return WasteDetectionResult(
      label: cleanLabel,
      confidence: 0.85 + (_random.nextDouble() * 0.14), // Random confidence 0.85-0.99
      suggestions: _getUpcyclingSuggestions(cleanLabel),
    );
  }

  // Real ML inference will be implemented when TensorFlow Lite package is stable
  // Your model.tflite is ready and waiting in assets/ml_models/model.tflite

  static void dispose() {
    _labels = null;
    _isInitialized = false;
  }
}

class WasteDetectionResult {
  final String label;
  final double confidence;
  final List<String> suggestions;

  WasteDetectionResult({
    required this.label,
    required this.confidence,
    required this.suggestions,
  });
}
