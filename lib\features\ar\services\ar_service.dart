import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/ar_object.dart';
import '../models/ar_scene.dart';

/// Main AR service for managing AR functionality across the app
class ARService {
  static ARService? _instance;
  static ARService get instance => _instance ??= ARService._();
  
  ARService._();

  bool _isInitialized = false;
  bool _isARSupported = false;
  ARScene? _currentScene;

  /// Check if AR is supported on the current device
  Future<bool> checkARSupport() async {
    try {
      if (kIsWeb) {
        // Web AR support check
        return await _checkWebARSupport();
      } else {
        // Mobile AR support check
        return await _checkMobileARSupport();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking AR support: $e');
      }
      return false;
    }
  }

  /// Initialize AR service
  Future<bool> initialize() async {
    if (_isInitialized) return _isARSupported;

    try {
      _isARSupported = await checkARSupport();
      
      if (_isARSupported) {
        await _initializeARSession();
        _isInitialized = true;
        
        if (kDebugMode) {
          print('✅ AR Service initialized successfully');
        }
      } else {
        if (kDebugMode) {
          print('❌ AR not supported on this device');
        }
      }
      
      return _isARSupported;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize AR Service: $e');
      }
      return false;
    }
  }

  /// Check Web AR support
  Future<bool> _checkWebARSupport() async {
    // For web, we'll use WebXR or model-viewer
    // This is a placeholder - actual implementation would check WebXR support
    if (kDebugMode) {
      print('🌐 Checking Web AR support...');
      print('Note: Web AR requires WebXR-compatible browser');
      print('Recommended: Chrome 79+, Edge 79+, Firefox with WebXR enabled');
    }
    
    // Return false for now as WebXR implementation is complex
    // In production, you would check: navigator.xr?.isSessionSupported('immersive-ar')
    return false;
  }

  /// Check Mobile AR support
  Future<bool> _checkMobileARSupport() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        // Check ARCore support
        return await _checkARCoreSupport();
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        // Check ARKit support
        return await _checkARKitSupport();
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking mobile AR support: $e');
      }
      return false;
    }
  }

  /// Check ARCore support (Android)
  Future<bool> _checkARCoreSupport() async {
    // This would use arcore_flutter_plugin to check support
    // For now, we'll simulate the check
    if (kDebugMode) {
      print('📱 Checking ARCore support...');
      print('Requirements: Android 7.0+, ARCore-compatible device');
    }
    
    // Placeholder implementation
    // In production: return await ArCoreController.checkArCoreAvailability();
    return true; // Assume supported for demo
  }

  /// Check ARKit support (iOS)
  Future<bool> _checkARKitSupport() async {
    // This would use arkit_plugin to check support
    // For now, we'll simulate the check
    if (kDebugMode) {
      print('📱 Checking ARKit support...');
      print('Requirements: iOS 11.0+, A9 processor or newer');
    }
    
    // Placeholder implementation
    // In production: return await ARKitController.checkDeviceSupport();
    return true; // Assume supported for demo
  }

  /// Initialize AR session
  Future<void> _initializeARSession() async {
    _currentScene = ARScene(
      id: 'main_scene',
      objects: [],
      lighting: ARLighting.automatic,
      planeDetection: ARPlaneDetection.horizontal,
    );
    
    if (kDebugMode) {
      print('🎬 AR Session initialized');
    }
  }

  /// Start AR session for product preview
  Future<ARSession?> startProductPreview(String productId) async {
    if (!_isInitialized || !_isARSupported) {
      throw ARException('AR not available');
    }

    try {
      final session = ARSession(
        id: 'product_preview_$productId',
        type: ARSessionType.productPreview,
        productId: productId,
      );

      if (kDebugMode) {
        print('🛍️ Starting product preview AR session for: $productId');
      }

      return session;
    } catch (e) {
      if (kDebugMode) {
        print('Error starting product preview: $e');
      }
      return null;
    }
  }

  /// Start AR session for upcycling project
  Future<ARSession?> startUpcyclingProject(String projectId) async {
    if (!_isInitialized || !_isARSupported) {
      throw ARException('AR not available');
    }

    try {
      final session = ARSession(
        id: 'upcycling_$projectId',
        type: ARSessionType.upcyclingProject,
        projectId: projectId,
      );

      if (kDebugMode) {
        print('♻️ Starting upcycling project AR session for: $projectId');
      }

      return session;
    } catch (e) {
      if (kDebugMode) {
        print('Error starting upcycling project: $e');
      }
      return null;
    }
  }

  /// Add object to AR scene
  Future<bool> addObjectToScene(ARObject object) async {
    if (_currentScene == null) return false;

    try {
      _currentScene!.objects.add(object);
      
      if (kDebugMode) {
        print('➕ Added object to AR scene: ${object.id}');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error adding object to scene: $e');
      }
      return false;
    }
  }

  /// Remove object from AR scene
  Future<bool> removeObjectFromScene(String objectId) async {
    if (_currentScene == null) return false;

    try {
      _currentScene!.objects.removeWhere((obj) => obj.id == objectId);
      
      if (kDebugMode) {
        print('➖ Removed object from AR scene: $objectId');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error removing object from scene: $e');
      }
      return false;
    }
  }

  /// Get current AR scene
  ARScene? get currentScene => _currentScene;

  /// Check if AR is initialized and supported
  bool get isAvailable => _isInitialized && _isARSupported;

  /// Dispose AR service
  void dispose() {
    _currentScene = null;
    _isInitialized = false;
    if (kDebugMode) {
      print('🧹 AR Service disposed');
    }
  }
}

/// AR Session class
class ARSession {
  final String id;
  final ARSessionType type;
  final String? productId;
  final String? projectId;
  final DateTime startTime;

  ARSession({
    required this.id,
    required this.type,
    this.productId,
    this.projectId,
  }) : startTime = DateTime.now();

  Duration get duration => DateTime.now().difference(startTime);
}

/// AR Session types
enum ARSessionType {
  productPreview,
  upcyclingProject,
  materialIdentification,
  tutorial,
}

/// AR Exception class
class ARException implements Exception {
  final String message;
  ARException(this.message);
  
  @override
  String toString() => 'ARException: $message';
}

/// AR Lighting modes
enum ARLighting {
  automatic,
  manual,
  environmental,
}

/// AR Plane detection modes
enum ARPlaneDetection {
  none,
  horizontal,
  vertical,
  both,
}

/// AR Service Provider for dependency injection
class ARServiceProvider {
  static ARService? _service;
  
  static ARService get service {
    _service ??= ARService.instance;
    return _service!;
  }
  
  static Future<bool> initialize() async {
    return await service.initialize();
  }
  
  static bool get isAvailable => service.isAvailable;
  
  static void dispose() {
    _service?.dispose();
    _service = null;
  }
}
