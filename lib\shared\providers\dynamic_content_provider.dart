import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/product_model.dart';
import '../models/user_model.dart';
import '../../core/services/firebase_service.dart';
import '../../core/services/firestore_schema_service.dart';

// Cache duration for different content types
const Duration _shortCacheDuration = Duration(minutes: 5);
const Duration _mediumCacheDuration = Duration(minutes: 15);
const Duration _longCacheDuration = Duration(hours: 1);

// Featured products provider with caching
final featuredProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  try {
    final snapshot = await FirebaseService.firestore
        .collection('products')
        .where('isAvailable', isEqualTo: true)
        .where('rating', isGreaterThanOrEqualTo: 4.0)
        .orderBy('rating', descending: true)
        .orderBy('stats.views', descending: true)
        .limit(10)
        .get();

    final products = snapshot.docs
        .map((doc) => ProductModel.fromFirestore(doc))
        .toList();

    if (products.isEmpty) {
      // Fallback to sample data
      return ProductService.getSampleProducts().take(6).toList();
    }

    return products;
  } catch (e) {
    if (kDebugMode) {
      print('Error fetching featured products: $e');
    }
    // Return sample data on error
    return ProductService.getSampleProducts().take(6).toList();
  }
});

// Categories provider with product counts
final categoriesProvider = FutureProvider<List<CategoryModel>>((ref) async {
  try {
    final snapshot = await FirebaseService.firestore
        .collection('categories')
        .where('isActive', isEqualTo: true)
        .orderBy('name')
        .get();

    final categories = snapshot.docs.map((doc) {
      final data = doc.data();
      return CategoryModel(
        id: doc.id,
        name: data['name'] ?? '',
        description: data['description'] ?? '',
        imageUrl: data['imageUrl'] ?? '',
        productCount: data['productCount'] ?? 0,
        isActive: data['isActive'] ?? true,
      );
    }).toList();

    if (categories.isEmpty) {
      // Return default categories
      return ProductCategory.values.map((category) => CategoryModel(
        id: category.name,
        name: category.displayName,
        description: 'Products in ${category.displayName} category',
        imageUrl: 'assets/images/categories/${category.name}.png',
        productCount: 0,
        isActive: true,
      )).toList();
    }

    return categories;
  } catch (e) {
    if (kDebugMode) {
      print('Error fetching categories: $e');
    }
    // Return default categories on error
    return ProductCategory.values.map((category) => CategoryModel(
      id: category.name,
      name: category.displayName,
      description: 'Products in ${category.displayName} category',
      imageUrl: 'assets/images/categories/${category.name}.png',
      productCount: 0,
      isActive: true,
    )).toList();
  }
});

// Trending products provider
final trendingProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  try {
    final snapshot = await FirebaseService.firestore
        .collection('products')
        .where('isAvailable', isEqualTo: true)
        .orderBy('stats.views', descending: true)
        .limit(8)
        .get();

    final products = snapshot.docs
        .map((doc) => ProductModel.fromFirestore(doc))
        .toList();

    if (products.isEmpty) {
      return ProductService.getSampleProducts().take(4).toList();
    }

    return products;
  } catch (e) {
    if (kDebugMode) {
      print('Error fetching trending products: $e');
    }
    return ProductService.getSampleProducts().take(4).toList();
  }
});

// User statistics provider
final userStatsProvider = FutureProvider<UserStats>((ref) async {
  final currentUser = ref.watch(currentUserProvider).value;
  
  if (currentUser == null) {
    return UserStats(
      totalOrders: 0,
      totalSpent: 0.0,
      favoriteProducts: 0,
      reviewsGiven: 0,
    );
  }

  try {
    // Get user orders
    final ordersSnapshot = await FirebaseService.firestore
        .collection('orders')
        .where('userId', isEqualTo: currentUser.id)
        .get();

    // Get user reviews
    final reviewsSnapshot = await FirebaseService.firestore
        .collection('reviews')
        .where('userId', isEqualTo: currentUser.id)
        .get();

    // Get user favorites
    final favoritesSnapshot = await FirebaseService.firestore
        .collection('users')
        .doc(currentUser.id)
        .collection('favorites')
        .get();

    double totalSpent = 0.0;
    for (final doc in ordersSnapshot.docs) {
      final data = doc.data();
      totalSpent += (data['total'] ?? 0.0).toDouble();
    }

    return UserStats(
      totalOrders: ordersSnapshot.docs.length,
      totalSpent: totalSpent,
      favoriteProducts: favoritesSnapshot.docs.length,
      reviewsGiven: reviewsSnapshot.docs.length,
    );
  } catch (e) {
    if (kDebugMode) {
      print('Error fetching user stats: $e');
    }
    return UserStats(
      totalOrders: 0,
      totalSpent: 0.0,
      favoriteProducts: 0,
      reviewsGiven: 0,
    );
  }
});

// App configuration provider
final appConfigProvider = FutureProvider<AppConfig>((ref) async {
  try {
    final doc = await FirebaseService.firestore
        .collection('config')
        .doc('app')
        .get();

    if (doc.exists) {
      final data = doc.data()!;
      return AppConfig(
        maintenanceMode: data['maintenanceMode'] ?? false,
        minAppVersion: data['minAppVersion'] ?? '1.0.0',
        featuresEnabled: Map<String, bool>.from(data['featuresEnabled'] ?? {}),
        apiEndpoints: Map<String, String>.from(data['apiEndpoints'] ?? {}),
        supportEmail: data['supportEmail'] ?? '<EMAIL>',
        supportPhone: data['supportPhone'] ?? '******-567-8900',
      );
    }

    // Return default config
    return AppConfig(
      maintenanceMode: false,
      minAppVersion: '1.0.0',
      featuresEnabled: {
        'chat_assistant': true,
        'ar_visualization': true,
        'social_features': true,
        'marketplace': true,
      },
      apiEndpoints: {
        'gemini_api': 'https://generativelanguage.googleapis.com',
      },
      supportEmail: '<EMAIL>',
      supportPhone: '******-567-8900',
    );
  } catch (e) {
    if (kDebugMode) {
      print('Error fetching app config: $e');
    }
    // Return default config on error
    return AppConfig(
      maintenanceMode: false,
      minAppVersion: '1.0.0',
      featuresEnabled: {
        'chat_assistant': true,
        'ar_visualization': true,
        'social_features': true,
        'marketplace': true,
      },
      apiEndpoints: {
        'gemini_api': 'https://generativelanguage.googleapis.com',
      },
      supportEmail: '<EMAIL>',
      supportPhone: '******-567-8900',
    );
  }
});

// Content loading state provider
final contentLoadingProvider = StateProvider<Map<String, bool>>((ref) => {});

// Cache management provider
final cacheManagerProvider = Provider<CacheManager>((ref) => CacheManager());

class CacheManager {
  final Map<String, CacheEntry> _cache = {};

  T? get<T>(String key) {
    final entry = _cache[key];
    if (entry == null || entry.isExpired) {
      _cache.remove(key);
      return null;
    }
    return entry.data as T?;
  }

  void set<T>(String key, T data, Duration duration) {
    _cache[key] = CacheEntry(
      data: data,
      expiresAt: DateTime.now().add(duration),
    );
  }

  void clear([String? key]) {
    if (key != null) {
      _cache.remove(key);
    } else {
      _cache.clear();
    }
  }

  void clearExpired() {
    _cache.removeWhere((key, entry) => entry.isExpired);
  }
}

class CacheEntry {
  final dynamic data;
  final DateTime expiresAt;

  CacheEntry({required this.data, required this.expiresAt});

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}

// Data models for dynamic content
class CategoryModel {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final int productCount;
  final bool isActive;

  CategoryModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.productCount,
    required this.isActive,
  });
}

class UserStats {
  final int totalOrders;
  final double totalSpent;
  final int favoriteProducts;
  final int reviewsGiven;

  UserStats({
    required this.totalOrders,
    required this.totalSpent,
    required this.favoriteProducts,
    required this.reviewsGiven,
  });
}

class AppConfig {
  final bool maintenanceMode;
  final String minAppVersion;
  final Map<String, bool> featuresEnabled;
  final Map<String, String> apiEndpoints;
  final String supportEmail;
  final String supportPhone;

  AppConfig({
    required this.maintenanceMode,
    required this.minAppVersion,
    required this.featuresEnabled,
    required this.apiEndpoints,
    required this.supportEmail,
    required this.supportPhone,
  });

  bool isFeatureEnabled(String feature) {
    return featuresEnabled[feature] ?? false;
  }
}
