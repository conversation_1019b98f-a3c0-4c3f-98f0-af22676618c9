import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../shared/models/product_model.dart';
import '../../shared/models/user_model.dart';
import 'firebase_service.dart';

/// Service to set up Firestore database schema and indexes
class FirestoreSchemaService {
  static final FirebaseFirestore _firestore = FirebaseService.firestore;

  /// Initialize Firestore schema with sample data and indexes
  static Future<void> initializeSchema() async {
    try {
      if (kDebugMode) {
        print('🔧 Initializing Firestore schema...');
      }

      // Create collections with sample data if they don't exist
      await _createProductsCollection();
      await _createUsersCollection();
      await _createCategoriesCollection();
      await _createOrdersCollection();
      await _createReviewsCollection();

      if (kDebugMode) {
        print('✅ Firestore schema initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing Firestore schema: $e');
      }
    }
  }

  /// Create products collection with sample data
  static Future<void> _createProductsCollection() async {
    final productsRef = _firestore.collection('products');
    final snapshot = await productsRef.limit(1).get();

    if (snapshot.docs.isEmpty) {
      if (kDebugMode) {
        print('📦 Creating products collection with sample data...');
      }

      final sampleProducts = ProductService.getSampleProducts();
      final batch = _firestore.batch();

      for (final product in sampleProducts) {
        final docRef = productsRef.doc(product.id);
        batch.set(docRef, product.toFirestore());
      }

      await batch.commit();

      if (kDebugMode) {
        print('✅ Products collection created with ${sampleProducts.length} sample products');
      }
    }
  }

  /// Create users collection structure
  static Future<void> _createUsersCollection() async {
    // Users collection will be created automatically when users sign up
    // This method sets up any default user-related documents if needed
    
    if (kDebugMode) {
      print('👥 Users collection structure ready');
    }
  }

  /// Create categories collection
  static Future<void> _createCategoriesCollection() async {
    final categoriesRef = _firestore.collection('categories');
    final snapshot = await categoriesRef.limit(1).get();

    if (snapshot.docs.isEmpty) {
      if (kDebugMode) {
        print('📂 Creating categories collection...');
      }

      final categories = ProductCategory.values.map((category) => {
        'id': category.name,
        'name': category.displayName,
        'description': 'Products in ${category.displayName} category',
        'imageUrl': 'assets/images/categories/${category.name}.png',
        'productCount': 0,
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      }).toList();

      final batch = _firestore.batch();
      for (final category in categories) {
        final docRef = categoriesRef.doc(category['id'] as String);
        batch.set(docRef, category);
      }

      await batch.commit();

      if (kDebugMode) {
        print('✅ Categories collection created with ${categories.length} categories');
      }
    }
  }

  /// Create orders collection structure
  static Future<void> _createOrdersCollection() async {
    // Orders collection will be created when users place orders
    // This method can set up any default order-related documents if needed
    
    if (kDebugMode) {
      print('🛒 Orders collection structure ready');
    }
  }

  /// Create reviews collection structure
  static Future<void> _createReviewsCollection() async {
    // Reviews collection will be created when users leave reviews
    // This method can set up any default review-related documents if needed
    
    if (kDebugMode) {
      print('⭐ Reviews collection structure ready');
    }
  }

  /// Set up Firestore security rules (for reference - these need to be set in Firebase Console)
  static String getSecurityRules() {
    return '''
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Products are readable by all authenticated users
    // Only product owners can write to their products
    match /products/{productId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (request.auth.uid == resource.data.sellerId || 
         request.auth.uid == request.resource.data.sellerId);
    }
    
    // Categories are readable by all authenticated users
    // Only admins can write (implement admin check as needed)
    match /categories/{categoryId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null; // Add admin check here
    }
    
    // Orders are readable/writable by the order owner
    match /orders/{orderId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Reviews are readable by all, writable by authenticated users
    match /reviews/{reviewId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Chat messages (for AI assistant)
    match /chats/{userId}/messages/{messageId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
''';
  }

  /// Set up Firestore indexes (for reference - these need to be created in Firebase Console)
  static List<Map<String, dynamic>> getRequiredIndexes() {
    return [
      {
        'collection': 'products',
        'fields': [
          {'field': 'isAvailable', 'order': 'ASCENDING'},
          {'field': 'createdAt', 'order': 'DESCENDING'},
        ],
      },
      {
        'collection': 'products',
        'fields': [
          {'field': 'category', 'order': 'ASCENDING'},
          {'field': 'price', 'order': 'ASCENDING'},
        ],
      },
      {
        'collection': 'products',
        'fields': [
          {'field': 'sellerId', 'order': 'ASCENDING'},
          {'field': 'createdAt', 'order': 'DESCENDING'},
        ],
      },
      {
        'collection': 'products',
        'fields': [
          {'field': 'tags', 'order': 'ASCENDING'},
          {'field': 'rating', 'order': 'DESCENDING'},
        ],
      },
      {
        'collection': 'orders',
        'fields': [
          {'field': 'userId', 'order': 'ASCENDING'},
          {'field': 'createdAt', 'order': 'DESCENDING'},
        ],
      },
      {
        'collection': 'reviews',
        'fields': [
          {'field': 'productId', 'order': 'ASCENDING'},
          {'field': 'createdAt', 'order': 'DESCENDING'},
        ],
      },
    ];
  }

  /// Update product statistics
  static Future<void> updateProductStats(String productId, {
    int? viewIncrement,
    int? orderIncrement,
    double? revenueIncrement,
    int? favoriteIncrement,
  }) async {
    try {
      final productRef = _firestore.collection('products').doc(productId);
      
      final updates = <String, dynamic>{};
      if (viewIncrement != null) {
        updates['stats.views'] = FieldValue.increment(viewIncrement);
      }
      if (orderIncrement != null) {
        updates['stats.orders'] = FieldValue.increment(orderIncrement);
      }
      if (revenueIncrement != null) {
        updates['stats.revenue'] = FieldValue.increment(revenueIncrement);
      }
      if (favoriteIncrement != null) {
        updates['stats.favorites'] = FieldValue.increment(favoriteIncrement);
      }
      
      if (updates.isNotEmpty) {
        updates['updatedAt'] = FieldValue.serverTimestamp();
        await productRef.update(updates);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating product stats: $e');
      }
    }
  }

  /// Update category product count
  static Future<void> updateCategoryProductCount(String categoryName, int increment) async {
    try {
      final categoryRef = _firestore.collection('categories').doc(categoryName.toLowerCase());
      await categoryRef.update({
        'productCount': FieldValue.increment(increment),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error updating category product count: $e');
      }
    }
  }

  /// Clean up old data (for maintenance)
  static Future<void> cleanupOldData() async {
    try {
      // Clean up old chat messages (older than 30 days)
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      
      // This would need to be implemented with cloud functions for large datasets
      if (kDebugMode) {
        print('🧹 Data cleanup completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error during data cleanup: $e');
      }
    }
  }
}
