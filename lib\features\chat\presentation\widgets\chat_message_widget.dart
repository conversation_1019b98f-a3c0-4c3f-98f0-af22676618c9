import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';
import '../../models/chat_message.dart';

class ChatMessageWidget extends StatelessWidget {
  final ChatMessage message;
  final Function(String) onSuggestionTap;
  final Function(String, Map<String, String>?) onDeepLinkTap;

  const ChatMessageWidget({
    super.key,
    required this.message,
    required this.onSuggestionTap,
    required this.onDeepLinkTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            _buildAvatar(),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: message.isUser 
                  ? CrossAxisAlignment.end 
                  : CrossAxisAlignment.start,
              children: [
                _buildMessageBubble(context),
                if (message.hasSuggestions && !message.isUser) ...[
                  const SizedBox(height: 8),
                  _buildSuggestions(),
                ],
                const SizedBox(height: 4),
                _buildTimestamp(),
              ],
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 12),
            _buildUserAvatar(),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: message.isError 
            ? AppTheme.errorColor 
            : AppTheme.primaryGreen,
        shape: BoxShape.circle,
      ),
      child: Icon(
        message.isError 
            ? Icons.error_outline 
            : Icons.smart_toy,
        color: Colors.white,
        size: 16,
      ),
    );
  }

  Widget _buildUserAvatar() {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: AppTheme.lightGreen.withOpacity(0.3),
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.person,
        color: AppTheme.primaryGreen,
        size: 16,
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context) {
    return GestureDetector(
      onLongPress: () => _showMessageOptions(context),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: message.isUser 
              ? AppTheme.primaryGreen 
              : Colors.white,
          borderRadius: BorderRadius.circular(20).copyWith(
            bottomLeft: message.isUser 
                ? const Radius.circular(20) 
                : const Radius.circular(4),
            bottomRight: message.isUser 
                ? const Radius.circular(4) 
                : const Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMessageContent(),
            if (message.hasImage) ...[
              const SizedBox(height: 8),
              _buildImageContent(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMessageContent() {
    return SelectableText(
      message.content,
      style: AppTextStyles.bodyMedium.copyWith(
        color: message.isUser ? Colors.white : AppTheme.textPrimary,
        height: 1.4,
      ),
      onTap: () => _handleContentTap(),
    );
  }

  Widget _buildImageContent() {
    if (!message.hasImage) return const SizedBox.shrink();

    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Image.network(
        message.imageUrl!,
        width: double.infinity,
        height: 200,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: double.infinity,
            height: 200,
            color: Colors.grey.shade200,
            child: const Center(
              child: Icon(
                Icons.broken_image,
                color: Colors.grey,
                size: 48,
              ),
            ),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: double.infinity,
            height: 200,
            color: Colors.grey.shade200,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSuggestions() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: message.suggestions.map((suggestion) {
        return GestureDetector(
          onTap: () => onSuggestionTap(suggestion),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppTheme.lightGreen.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppTheme.primaryGreen.withOpacity(0.3),
              ),
            ),
            child: Text(
              suggestion,
              style: AppTextStyles.caption.copyWith(
                color: AppTheme.primaryGreen,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTimestamp() {
    return Padding(
      padding: EdgeInsets.only(
        left: message.isUser ? 0 : 44,
        right: message.isUser ? 44 : 0,
        top: 2,
      ),
      child: Text(
        message.formattedTime,
        style: AppTextStyles.caption.copyWith(
          color: AppTheme.textSecondary,
          fontSize: 10,
        ),
      ),
    );
  }

  void _handleContentTap() {
    // Check for deep link actions in the message content
    final action = DeepLinkActions.extractActionFromText(message.content);
    if (action != null) {
      onDeepLinkTap(action, null);
    }
  }

  void _showMessageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            ListTile(
              leading: const Icon(Icons.copy, color: AppTheme.primaryGreen),
              title: const Text('Copy Message'),
              onTap: () {
                Clipboard.setData(ClipboardData(text: message.content));
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Message copied to clipboard'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
            ),
            
            if (!message.isUser) ...[
              ListTile(
                leading: const Icon(Icons.refresh, color: AppTheme.primaryGreen),
                title: const Text('Regenerate Response'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Implement regenerate response
                },
              ),
              
              ListTile(
                leading: const Icon(Icons.thumb_up_outlined, color: AppTheme.primaryGreen),
                title: const Text('Helpful'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Implement feedback
                },
              ),
              
              ListTile(
                leading: const Icon(Icons.thumb_down_outlined, color: AppTheme.primaryGreen),
                title: const Text('Not Helpful'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Implement feedback
                },
              ),
            ],
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
