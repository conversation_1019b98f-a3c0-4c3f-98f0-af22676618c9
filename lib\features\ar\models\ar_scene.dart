import 'ar_object.dart';
import '../services/ar_service.dart';

/// Represents an AR scene containing multiple objects and configuration
class ARScene {
  final String id;
  final List<ARObject> objects;
  final ARLighting lighting;
  final ARPlaneDetection planeDetection;
  final AREnvironment? environment;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  DateTime updatedAt;

  ARScene({
    required this.id,
    required this.objects,
    this.lighting = ARLighting.automatic,
    this.planeDetection = ARPlaneDetection.horizontal,
    this.environment,
    this.metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Add an object to the scene
  void addObject(ARObject object) {
    objects.add(object);
    updatedAt = DateTime.now();
  }

  /// Remove an object from the scene
  bool removeObject(String objectId) {
    final removed = objects.removeWhere((obj) => obj.id == objectId);
    if (removed > 0) {
      updatedAt = DateTime.now();
      return true;
    }
    return false;
  }

  /// Get object by ID
  ARObject? getObject(String objectId) {
    try {
      return objects.firstWhere((obj) => obj.id == objectId);
    } catch (e) {
      return null;
    }
  }

  /// Get all objects of a specific type
  List<ARObject> getObjectsByType(String type) {
    return objects.where((obj) {
      final metadata = obj.metadata;
      return metadata != null && metadata['type'] == type;
    }).toList();
  }

  /// Clear all objects from the scene
  void clearObjects() {
    objects.clear();
    updatedAt = DateTime.now();
  }

  /// Get scene bounds (min/max positions)
  ARBounds get bounds {
    if (objects.isEmpty) {
      return ARBounds(
        min: ARVector3.zero,
        max: ARVector3.zero,
      );
    }

    double minX = objects.first.position.x;
    double minY = objects.first.position.y;
    double minZ = objects.first.position.z;
    double maxX = objects.first.position.x;
    double maxY = objects.first.position.y;
    double maxZ = objects.first.position.z;

    for (final obj in objects) {
      final pos = obj.position;
      if (pos.x < minX) minX = pos.x;
      if (pos.y < minY) minY = pos.y;
      if (pos.z < minZ) minZ = pos.z;
      if (pos.x > maxX) maxX = pos.x;
      if (pos.y > maxY) maxY = pos.y;
      if (pos.z > maxZ) maxZ = pos.z;
    }

    return ARBounds(
      min: ARVector3(minX, minY, minZ),
      max: ARVector3(maxX, maxY, maxZ),
    );
  }

  /// Get scene center point
  ARVector3 get center {
    final sceneBounds = bounds;
    return ARVector3(
      (sceneBounds.min.x + sceneBounds.max.x) / 2,
      (sceneBounds.min.y + sceneBounds.max.y) / 2,
      (sceneBounds.min.z + sceneBounds.max.z) / 2,
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'objects': objects.map((obj) => obj.toJson()).toList(),
      'lighting': lighting.name,
      'planeDetection': planeDetection.name,
      'environment': environment?.toJson(),
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create from JSON
  factory ARScene.fromJson(Map<String, dynamic> json) {
    return ARScene(
      id: json['id'],
      objects: (json['objects'] as List)
          .map((obj) => ARObject.fromJson(obj))
          .toList(),
      lighting: ARLighting.values.firstWhere(
        (l) => l.name == json['lighting'],
        orElse: () => ARLighting.automatic,
      ),
      planeDetection: ARPlaneDetection.values.firstWhere(
        (p) => p.name == json['planeDetection'],
        orElse: () => ARPlaneDetection.horizontal,
      ),
      environment: json['environment'] != null
          ? AREnvironment.fromJson(json['environment'])
          : null,
      metadata: json['metadata'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Create a copy of this scene
  ARScene copyWith({
    String? id,
    List<ARObject>? objects,
    ARLighting? lighting,
    ARPlaneDetection? planeDetection,
    AREnvironment? environment,
    Map<String, dynamic>? metadata,
  }) {
    return ARScene(
      id: id ?? this.id,
      objects: objects ?? List.from(this.objects),
      lighting: lighting ?? this.lighting,
      planeDetection: planeDetection ?? this.planeDetection,
      environment: environment ?? this.environment,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'ARScene(id: $id, objects: ${objects.length}, lighting: $lighting)';
  }
}

/// Represents the AR environment settings
class AREnvironment {
  final String? skyboxTexture;
  final ARColor ambientColor;
  final double ambientIntensity;
  final List<ARLight> lights;

  AREnvironment({
    this.skyboxTexture,
    this.ambientColor = const ARColor(0.2, 0.2, 0.2, 1.0),
    this.ambientIntensity = 1.0,
    this.lights = const [],
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'skyboxTexture': skyboxTexture,
      'ambientColor': ambientColor.toJson(),
      'ambientIntensity': ambientIntensity,
      'lights': lights.map((light) => light.toJson()).toList(),
    };
  }

  /// Create from JSON
  factory AREnvironment.fromJson(Map<String, dynamic> json) {
    return AREnvironment(
      skyboxTexture: json['skyboxTexture'],
      ambientColor: json['ambientColor'] != null
          ? ARColor.fromJson(json['ambientColor'])
          : const ARColor(0.2, 0.2, 0.2, 1.0),
      ambientIntensity: (json['ambientIntensity'] ?? 1.0).toDouble(),
      lights: (json['lights'] as List? ?? [])
          .map((light) => ARLight.fromJson(light))
          .toList(),
    );
  }
}

/// Represents a light source in the AR scene
class ARLight {
  final String id;
  final ARLightType type;
  final ARVector3 position;
  final ARVector3 direction;
  final ARColor color;
  final double intensity;
  final double range;

  ARLight({
    required this.id,
    required this.type,
    this.position = ARVector3.zero,
    this.direction = const ARVector3(0, -1, 0),
    this.color = ARColor.white,
    this.intensity = 1.0,
    this.range = 10.0,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'position': position.toJson(),
      'direction': direction.toJson(),
      'color': color.toJson(),
      'intensity': intensity,
      'range': range,
    };
  }

  /// Create from JSON
  factory ARLight.fromJson(Map<String, dynamic> json) {
    return ARLight(
      id: json['id'],
      type: ARLightType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => ARLightType.directional,
      ),
      position: ARVector3.fromJson(json['position']),
      direction: ARVector3.fromJson(json['direction']),
      color: ARColor.fromJson(json['color']),
      intensity: (json['intensity'] ?? 1.0).toDouble(),
      range: (json['range'] ?? 10.0).toDouble(),
    );
  }
}

/// Types of lights in AR
enum ARLightType {
  directional,
  point,
  spot,
  ambient,
}

/// Represents the bounds of an AR scene
class ARBounds {
  final ARVector3 min;
  final ARVector3 max;

  ARBounds({
    required this.min,
    required this.max,
  });

  /// Get the size of the bounds
  ARVector3 get size => max - min;

  /// Get the center of the bounds
  ARVector3 get center => min + (size / 2);

  /// Check if a point is within the bounds
  bool contains(ARVector3 point) {
    return point.x >= min.x && point.x <= max.x &&
           point.y >= min.y && point.y <= max.y &&
           point.z >= min.z && point.z <= max.z;
  }

  @override
  String toString() => 'ARBounds(min: $min, max: $max)';
}

/// Predefined AR scene templates
class ARSceneTemplates {
  /// Create a product showcase scene
  static ARScene createProductShowcase(String productId) {
    return ARScene(
      id: 'product_showcase_$productId',
      objects: [],
      lighting: ARLighting.environmental,
      planeDetection: ARPlaneDetection.horizontal,
      environment: AREnvironment(
        ambientColor: const ARColor(0.3, 0.3, 0.3, 1.0),
        ambientIntensity: 0.8,
        lights: [
          ARLight(
            id: 'main_light',
            type: ARLightType.directional,
            direction: const ARVector3(-0.5, -1, -0.5),
            color: ARColor.white,
            intensity: 1.2,
          ),
        ],
      ),
      metadata: {
        'type': 'product_showcase',
        'productId': productId,
      },
    );
  }

  /// Create an upcycling tutorial scene
  static ARScene createUpcyclingTutorial(String projectId) {
    return ARScene(
      id: 'upcycling_tutorial_$projectId',
      objects: [],
      lighting: ARLighting.automatic,
      planeDetection: ARPlaneDetection.both,
      environment: AREnvironment(
        ambientColor: const ARColor(0.4, 0.4, 0.4, 1.0),
        ambientIntensity: 1.0,
      ),
      metadata: {
        'type': 'upcycling_tutorial',
        'projectId': projectId,
      },
    );
  }

  /// Create a material identification scene
  static ARScene createMaterialIdentification() {
    return ARScene(
      id: 'material_identification_${DateTime.now().millisecondsSinceEpoch}',
      objects: [],
      lighting: ARLighting.automatic,
      planeDetection: ARPlaneDetection.horizontal,
      metadata: {
        'type': 'material_identification',
      },
    );
  }
}
