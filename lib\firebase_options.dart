// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDstBgDfTCSBgrR1Js3Lm2AKUsWiAirLyU',
    appId: '1:192005607529:web:b2b154cb1bfed8539492f8',
    messagingSenderId: '192005607529',
    projectId: 'ecocura-e5ddd',
    authDomain: 'ecocura-e5ddd.firebaseapp.com',
    storageBucket: 'ecocura-e5ddd.firebasestorage.app',
    measurementId: 'G-RF6HSQ64CQ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDstBgDfTCSBgrR1Js3Lm2AKUsWiAirLyU',
    appId: '1:192005607529:android:b2b154cb1bfed8539492f8',
    messagingSenderId: '192005607529',
    projectId: 'ecocura-e5ddd',
    authDomain: 'ecocura-e5ddd.firebaseapp.com',
    storageBucket: 'ecocura-e5ddd.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDstBgDfTCSBgrR1Js3Lm2AKUsWiAirLyU',
    appId: '1:192005607529:ios:b2b154cb1bfed8539492f8',
    messagingSenderId: '192005607529',
    projectId: 'ecocura-e5ddd',
    authDomain: 'ecocura-e5ddd.firebaseapp.com',
    storageBucket: 'ecocura-e5ddd.firebasestorage.app',
    iosClientId: '192005607529-example.apps.googleusercontent.com',
    iosBundleId: 'com.ecocura.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDstBgDfTCSBgrR1Js3Lm2AKUsWiAirLyU',
    appId: '1:192005607529:ios:b2b154cb1bfed8539492f8',
    messagingSenderId: '192005607529',
    projectId: 'ecocura-e5ddd',
    authDomain: 'ecocura-e5ddd.firebaseapp.com',
    storageBucket: 'ecocura-e5ddd.firebasestorage.app',
    iosClientId: '192005607529-example.apps.googleusercontent.com',
    iosBundleId: 'com.ecocura.app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDstBgDfTCSBgrR1Js3Lm2AKUsWiAirLyU',
    appId: '1:192005607529:web:b2b154cb1bfed8539492f8',
    messagingSenderId: '192005607529',
    projectId: 'ecocura-e5ddd',
    authDomain: 'ecocura-e5ddd.firebaseapp.com',
    storageBucket: 'ecocura-e5ddd.firebasestorage.app',
    measurementId: 'G-RF6HSQ64CQ',
  );
}
