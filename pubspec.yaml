name: ecocura_flutter
description: "Flutter version of UpCyclization - A waste management and upcycling app"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9

  # Navigation
  go_router: ^12.1.3

  # Firebase (optional - will handle gracefully if not configured)
  firebase_core: ^3.14.0
  firebase_auth: ^5.6.0
  cloud_firestore: ^5.6.9
  firebase_storage: ^12.4.7

  # Camera & Image Processing (camera temporarily disabled for Android build)
  # camera: ^0.11.1
  image_picker: ^1.1.2
  image: ^4.5.4

  # ML & AI
  tflite_flutter: ^0.11.0
  url_launcher: ^6.2.1

  # Video Playback
  youtube_player_flutter: ^9.1.1

  # UI Components
  cached_network_image: ^3.3.0
  carousel_slider: ^5.1.1
  flutter_staggered_grid_view: ^0.7.0
  shimmer: ^3.0.0

  # HTTP & Networking
  http: ^1.1.0
  dio: ^5.3.2

  # AI & ML
  google_generative_ai: ^0.4.3

  # AR Dependencies (commented out - uncomment when implementing AR)
  # arcore_flutter_plugin: ^0.0.9  # For Android AR
  # arkit_plugin: ^0.11.0          # For iOS AR
  # ar_flutter_plugin: ^0.7.3      # For unified AR

  # Additional utilities
  uuid: ^4.3.3                     # UUID generation

  # Utilities
  shared_preferences: ^2.3.3
  path_provider: ^2.1.5
  permission_handler: ^12.0.0+1
  intl: ^0.20.2

  # Icons & Fonts
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.7

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/ml_models/
    - assets/ml_models/
    - assets/fonts/

  # fonts:
  #   - family: CustomFont
  #     fonts:
  #       - asset: assets/fonts/CustomFont-Regular.ttf
  #       - asset: assets/fonts/CustomFont-Bold.ttf
  #         weight: 700
