import 'package:cloud_firestore/cloud_firestore.dart';

enum MessageType {
  text,
  image,
  suggestion,
  error,
  system,
}

class ChatMessage {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final MessageType messageType;
  final List<String> suggestions;
  final String? imageUrl;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.messageType = MessageType.text,
    this.suggestions = const [],
    this.imageUrl,
    this.metadata,
  });

  factory ChatMessage.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ChatMessage(
      id: doc.id,
      content: data['content'] ?? '',
      isUser: data['isUser'] ?? false,
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      messageType: MessageType.values.firstWhere(
        (type) => type.name == data['messageType'],
        orElse: () => MessageType.text,
      ),
      suggestions: List<String>.from(data['suggestions'] ?? []),
      imageUrl: data['imageUrl'],
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'content': content,
      'isUser': isUser,
      'timestamp': Timestamp.fromDate(timestamp),
      'messageType': messageType.name,
      'suggestions': suggestions,
      'imageUrl': imageUrl,
      'metadata': metadata,
    };
  }

  ChatMessage copyWith({
    String? id,
    String? content,
    bool? isUser,
    DateTime? timestamp,
    MessageType? messageType,
    List<String>? suggestions,
    String? imageUrl,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      messageType: messageType ?? this.messageType,
      suggestions: suggestions ?? this.suggestions,
      imageUrl: imageUrl ?? this.imageUrl,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasSuggestions => suggestions.isNotEmpty;
  bool get isError => messageType == MessageType.error;
  bool get isSystem => messageType == MessageType.system;

  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

class ChatSuggestion {
  final String id;
  final String text;
  final String? action;
  final Map<String, dynamic>? parameters;

  const ChatSuggestion({
    required this.id,
    required this.text,
    this.action,
    this.parameters,
  });

  factory ChatSuggestion.fromMap(Map<String, dynamic> map) {
    return ChatSuggestion(
      id: map['id'] ?? '',
      text: map['text'] ?? '',
      action: map['action'],
      parameters: map['parameters'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'text': text,
      'action': action,
      'parameters': parameters,
    };
  }
}

// Predefined quick actions
class QuickActions {
  static const List<ChatSuggestion> defaultActions = [
    ChatSuggestion(
      id: 'upcycling_ideas',
      text: '💡 Get upcycling ideas',
      action: 'show_upcycling_ideas',
    ),
    ChatSuggestion(
      id: 'how_to_guide',
      text: '📖 How-to guides',
      action: 'show_how_to_guides',
    ),
    ChatSuggestion(
      id: 'find_products',
      text: '🛍️ Find products',
      action: 'search_products',
    ),
    ChatSuggestion(
      id: 'identify_material',
      text: '📸 Identify material',
      action: 'identify_material',
    ),
    ChatSuggestion(
      id: 'sustainability_tips',
      text: '🌱 Sustainability tips',
      action: 'show_sustainability_tips',
    ),
  ];

  static List<ChatSuggestion> getContextualSuggestions(String context) {
    switch (context.toLowerCase()) {
      case 'upcycling':
        return [
          const ChatSuggestion(
            id: 'materials_needed',
            text: 'What materials do I need?',
          ),
          const ChatSuggestion(
            id: 'difficulty_level',
            text: 'How difficult is this project?',
          ),
          const ChatSuggestion(
            id: 'time_required',
            text: 'How long will this take?',
          ),
        ];
      case 'products':
        return [
          const ChatSuggestion(
            id: 'compare_prices',
            text: 'Compare prices',
          ),
          const ChatSuggestion(
            id: 'check_reviews',
            text: 'Check reviews',
          ),
          const ChatSuggestion(
            id: 'similar_products',
            text: 'Show similar products',
          ),
        ];
      case 'sustainability':
        return [
          const ChatSuggestion(
            id: 'reduce_waste',
            text: 'How to reduce waste?',
          ),
          const ChatSuggestion(
            id: 'eco_alternatives',
            text: 'Eco-friendly alternatives',
          ),
          const ChatSuggestion(
            id: 'carbon_footprint',
            text: 'Reduce carbon footprint',
          ),
        ];
      default:
        return defaultActions.take(3).toList();
    }
  }
}

// Message templates for different scenarios
class MessageTemplates {
  static ChatMessage welcomeMessage() {
    return ChatMessage(
      id: 'welcome_${DateTime.now().millisecondsSinceEpoch}',
      content: '''
👋 Welcome to EcoCura! I'm your AI assistant for sustainable living.

I can help you with:
• 🔄 Upcycling ideas and DIY projects
• 🛍️ Finding eco-friendly products
• ♻️ Waste reduction tips
• 📸 Identifying materials for upcycling

What would you like to explore today?
''',
      isUser: false,
      timestamp: DateTime.now(),
      messageType: MessageType.system,
      suggestions: QuickActions.defaultActions.map((a) => a.text).toList(),
    );
  }

  static ChatMessage errorMessage(String error) {
    return ChatMessage(
      id: 'error_${DateTime.now().millisecondsSinceEpoch}',
      content: '❌ $error',
      isUser: false,
      timestamp: DateTime.now(),
      messageType: MessageType.error,
      suggestions: ['Try again', 'Contact support'],
    );
  }

  static ChatMessage loadingMessage() {
    return ChatMessage(
      id: 'loading_${DateTime.now().millisecondsSinceEpoch}',
      content: '🤔 Let me think about that...',
      isUser: false,
      timestamp: DateTime.now(),
      messageType: MessageType.system,
    );
  }

  static ChatMessage typingIndicator() {
    return ChatMessage(
      id: 'typing_${DateTime.now().millisecondsSinceEpoch}',
      content: '💭 EcoBot is typing...',
      isUser: false,
      timestamp: DateTime.now(),
      messageType: MessageType.system,
    );
  }
}

// Deep linking actions
class DeepLinkActions {
  static const Map<String, String> actions = {
    'browse_marketplace': '/market',
    'view_product': '/product/{id}',
    'search_products': '/search?q={query}',
    'upcycling_projects': '/upcycle',
    'user_profile': '/profile',
    'create_product': '/add-product-listing',
    'view_category': '/market?category={category}',
    'how_to_guides': '/upcycle?tab=guides',
    'sustainability_tips': '/upcycle?tab=tips',
  };

  static String? getRouteForAction(String action, [Map<String, String>? params]) {
    final route = actions[action];
    if (route == null) return null;

    if (params != null) {
      String finalRoute = route;
      params.forEach((key, value) {
        finalRoute = finalRoute.replaceAll('{$key}', value);
      });
      return finalRoute;
    }

    return route;
  }

  static bool isDeepLinkAction(String text) {
    return actions.keys.any((action) => text.toLowerCase().contains(action.replaceAll('_', ' ')));
  }

  static String? extractActionFromText(String text) {
    for (final action in actions.keys) {
      if (text.toLowerCase().contains(action.replaceAll('_', ' '))) {
        return action;
      }
    }
    return null;
  }
}
