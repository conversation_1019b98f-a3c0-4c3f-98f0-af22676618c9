import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/product_model.dart';
import '../../services/ar_service.dart';
import '../../models/ar_object.dart';
import '../../models/ar_scene.dart';

class ARPreviewScreen extends ConsumerStatefulWidget {
  final String productId;
  
  const ARPreviewScreen({
    super.key,
    required this.productId,
  });

  @override
  ConsumerState<ARPreviewScreen> createState() => _ARPreviewScreenState();
}

class _ARPreviewScreenState extends ConsumerState<ARPreviewScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  bool _isARSupported = false;
  bool _isLoading = true;
  bool _isARActive = false;
  String? _errorMessage;
  ARSession? _currentSession;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _initializeAR();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _currentSession = null;
    super.dispose();
  }

  Future<void> _initializeAR() async {
    try {
      final arService = ARServiceProvider.service;
      _isARSupported = await arService.initialize();
      
      if (_isARSupported) {
        _animationController.forward();
      }
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to initialize AR: $e';
      });
    }
  }

  Future<void> _startARSession() async {
    if (!_isARSupported) return;

    try {
      setState(() {
        _isLoading = true;
      });

      final arService = ARServiceProvider.service;
      _currentSession = await arService.startProductPreview(widget.productId);
      
      if (_currentSession != null) {
        setState(() {
          _isARActive = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to start AR session: $e';
      });
    }
  }

  void _stopARSession() {
    setState(() {
      _isARActive = false;
      _currentSession = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => context.pop(),
        ),
        title: Text(
          'AR Preview',
          style: AppTextStyles.heading2.copyWith(
            color: Colors.white,
          ),
        ),
        actions: [
          if (_isARActive)
            IconButton(
              icon: const Icon(Icons.camera_alt, color: Colors.white),
              onPressed: _captureARPhoto,
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (!_isARSupported) {
      return _buildUnsupportedState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_isARActive) {
      return _buildARView();
    }

    return _buildWelcomeState();
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppTheme.primaryGreen,
          ),
          SizedBox(height: 24),
          Text(
            'Initializing AR...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnsupportedState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.phone_android,
              size: 80,
              color: Colors.white54,
            ),
            const SizedBox(height: 24),
            Text(
              'AR Not Supported',
              style: AppTextStyles.heading2.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Your device doesn\'t support AR features. You can still view the product in 2D mode.',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => context.pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
              child: const Text('View Product Details'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red,
            ),
            const SizedBox(height: 24),
            Text(
              'AR Error',
              style: AppTextStyles.heading2.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _initializeAR,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryGreen,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Try Again'),
                ),
                OutlinedButton(
                  onPressed: () => context.pop(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white),
                  ),
                  child: const Text('Go Back'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeState() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.view_in_ar,
                  size: 60,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const SizedBox(height: 32),
              Text(
                'AR Preview Ready',
                style: AppTextStyles.heading1.copyWith(
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'See how this product looks in your space using augmented reality.',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _startARSession,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryGreen,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.play_arrow),
                      SizedBox(width: 8),
                      Text(
                        'Start AR Experience',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              _buildARInstructions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildARInstructions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            'AR Instructions',
            style: AppTextStyles.bodyLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildInstructionItem(
            Icons.search,
            'Point your camera at a flat surface',
          ),
          _buildInstructionItem(
            Icons.touch_app,
            'Tap to place the product',
          ),
          _buildInstructionItem(
            Icons.gesture,
            'Pinch to resize, drag to move',
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppTheme.primaryGreen,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildARView() {
    // This would be the actual AR camera view
    // For now, we'll show a placeholder with controls
    return Stack(
      children: [
        // AR Camera View Placeholder
        Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.black,
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.videocam,
                  size: 80,
                  color: Colors.white54,
                ),
                SizedBox(height: 16),
                Text(
                  'AR Camera View',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Point camera at a flat surface',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // AR Controls
        Positioned(
          bottom: 32,
          left: 16,
          right: 16,
          child: _buildARControls(),
        ),
      ],
    );
  }

  Widget _buildARControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildControlButton(
            Icons.close,
            'Exit',
            _stopARSession,
          ),
          _buildControlButton(
            Icons.refresh,
            'Reset',
            () {
              // Reset AR scene
            },
          ),
          _buildControlButton(
            Icons.camera_alt,
            'Photo',
            _captureARPhoto,
          ),
          _buildControlButton(
            Icons.share,
            'Share',
            _shareARExperience,
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton(
    IconData icon,
    String label,
    VoidCallback onPressed,
  ) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _captureARPhoto() {
    // TODO: Implement AR photo capture
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AR photo capture coming soon!'),
        backgroundColor: AppTheme.primaryGreen,
      ),
    );
  }

  void _shareARExperience() {
    // TODO: Implement AR experience sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AR sharing coming soon!'),
        backgroundColor: AppTheme.primaryGreen,
      ),
    );
  }
}
