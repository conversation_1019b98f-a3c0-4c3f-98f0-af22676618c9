import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../models/chat_message.dart';
import '../models/chat_session.dart';
import '../../../shared/providers/auth_provider.dart';
import '../../../core/services/firebase_service.dart';

// Chat provider
final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>((ref) {
  return ChatNotifier(ref);
});

// Unread messages count provider
final unreadMessagesCountProvider = Provider<int>((ref) {
  final chatState = ref.watch(chatProvider);
  return chatState.unreadCount;
});

// Chat sessions provider
final chatSessionsProvider = StreamProvider<List<ChatSession>>((ref) {
  final currentUser = ref.watch(currentUserProvider).value;
  
  if (currentUser == null) {
    return Stream.value([]);
  }

  return FirebaseService.firestore
      .collection('users')
      .doc(currentUser.id)
      .collection('chat_sessions')
      .orderBy('lastMessageAt', descending: true)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => ChatSession.fromFirestore(doc))
          .toList());
});

class ChatState {
  final List<ChatMessage> messages;
  final bool isLoading;
  final String? error;
  final String? currentSessionId;
  final int unreadCount;

  ChatState({
    this.messages = const [],
    this.isLoading = false,
    this.error,
    this.currentSessionId,
    this.unreadCount = 0,
  });

  ChatState copyWith({
    List<ChatMessage>? messages,
    bool? isLoading,
    String? error,
    String? currentSessionId,
    int? unreadCount,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentSessionId: currentSessionId ?? this.currentSessionId,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}

class ChatNotifier extends StateNotifier<ChatState> {
  final Ref _ref;
  GenerativeModel? _geminiModel;
  static const String _apiKey = 'AIzaSyAV5MKNN0-WmFl-5AGU_vAAsGW34bpBJ5A';
  static const String _modelName = 'gemini-2.0-flash';

  ChatNotifier(this._ref) : super(ChatState()) {
    _initializeGemini();
    _loadCurrentSession();
  }

  void _initializeGemini() {
    try {
      _geminiModel = GenerativeModel(
        model: _modelName,
        apiKey: _apiKey,
        systemInstruction: Content.system(_getSystemPrompt()),
      );
      if (kDebugMode) {
        print('✅ Gemini AI chat model initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Gemini chat model: $e');
      }
    }
  }

  String _getSystemPrompt() {
    return '''
You are EcoBot, an AI assistant for EcoCura - a sustainable living and upcycling platform. Your role is to help users with:

1. **Upcycling Ideas**: Suggest creative ways to repurpose waste materials
2. **How-to Guides**: Provide step-by-step instructions for DIY projects
3. **Product Recommendations**: Help users find eco-friendly products in the marketplace
4. **Sustainability Tips**: Share advice on reducing waste and living sustainably
5. **Material Identification**: Help identify materials and their upcycling potential

**Guidelines:**
- Be enthusiastic and encouraging about sustainability
- Provide practical, actionable advice
- Include safety considerations for DIY projects
- Suggest products from the EcoCura marketplace when relevant
- Keep responses concise but informative
- Use emojis sparingly but effectively
- Always prioritize safety and environmental responsibility

**Context Awareness:**
- Remember the user's previous questions in this session
- Build upon previous conversations
- Suggest related projects or products based on user interests

**Deep Linking:**
When suggesting products or features, use these formats:
- Products: "Check out [product name] in our marketplace"
- Categories: "Browse our [category] section"
- Features: "Try our [feature name] feature"

Be helpful, friendly, and focused on promoting sustainable living!
''';
  }

  Future<void> _loadCurrentSession() async {
    final currentUser = _ref.read(currentUserProvider).value;
    if (currentUser == null) return;

    try {
      // Load the most recent session or create a new one
      final sessionsSnapshot = await FirebaseService.firestore
          .collection('users')
          .doc(currentUser.id)
          .collection('chat_sessions')
          .orderBy('lastMessageAt', descending: true)
          .limit(1)
          .get();

      if (sessionsSnapshot.docs.isNotEmpty) {
        final sessionDoc = sessionsSnapshot.docs.first;
        final sessionId = sessionDoc.id;
        
        // Load messages from this session
        final messagesSnapshot = await FirebaseService.firestore
            .collection('users')
            .doc(currentUser.id)
            .collection('chat_sessions')
            .doc(sessionId)
            .collection('messages')
            .orderBy('timestamp')
            .get();

        final messages = messagesSnapshot.docs
            .map((doc) => ChatMessage.fromFirestore(doc))
            .toList();

        state = state.copyWith(
          messages: messages,
          currentSessionId: sessionId,
        );
      } else {
        // Create a new session
        await _createNewSession();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading chat session: $e');
      }
    }
  }

  Future<void> _createNewSession() async {
    final currentUser = _ref.read(currentUserProvider).value;
    if (currentUser == null) return;

    try {
      final sessionRef = FirebaseService.firestore
          .collection('users')
          .doc(currentUser.id)
          .collection('chat_sessions')
          .doc();

      final session = ChatSession(
        id: sessionRef.id,
        userId: currentUser.id,
        title: 'New Chat',
        createdAt: DateTime.now(),
        lastMessageAt: DateTime.now(),
        messageCount: 0,
      );

      await sessionRef.set(session.toFirestore());

      state = state.copyWith(
        currentSessionId: sessionRef.id,
        messages: [],
      );

      // Send welcome message
      await _sendWelcomeMessage();
    } catch (e) {
      if (kDebugMode) {
        print('Error creating new chat session: $e');
      }
    }
  }

  Future<void> _sendWelcomeMessage() async {
    final welcomeMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: '''
👋 Hi there! I'm EcoBot, your sustainable living assistant!

I'm here to help you with:
🔄 Upcycling ideas and DIY projects
🛍️ Finding eco-friendly products
♻️ Waste reduction tips
📸 Identifying materials for upcycling

What would you like to explore today?
''',
      isUser: false,
      timestamp: DateTime.now(),
      messageType: MessageType.text,
    );

    await _addMessageToState(welcomeMessage);
  }

  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    final userMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content.trim(),
      isUser: true,
      timestamp: DateTime.now(),
      messageType: MessageType.text,
    );

    await _addMessageToState(userMessage);
    await _generateResponse(content);
  }

  Future<void> sendQuickAction(String action) async {
    await sendMessage(action);
  }

  Future<void> _addMessageToState(ChatMessage message) async {
    // Add to local state
    state = state.copyWith(
      messages: [...state.messages, message],
    );

    // Save to Firestore
    await _saveMessageToFirestore(message);
  }

  Future<void> _saveMessageToFirestore(ChatMessage message) async {
    final currentUser = _ref.read(currentUserProvider).value;
    if (currentUser == null || state.currentSessionId == null) return;

    try {
      await FirebaseService.firestore
          .collection('users')
          .doc(currentUser.id)
          .collection('chat_sessions')
          .doc(state.currentSessionId!)
          .collection('messages')
          .doc(message.id)
          .set(message.toFirestore());

      // Update session metadata
      await FirebaseService.firestore
          .collection('users')
          .doc(currentUser.id)
          .collection('chat_sessions')
          .doc(state.currentSessionId!)
          .update({
        'lastMessageAt': message.timestamp,
        'messageCount': state.messages.length,
        'title': _generateSessionTitle(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error saving message to Firestore: $e');
      }
    }
  }

  String _generateSessionTitle() {
    if (state.messages.isEmpty) return 'New Chat';
    
    final firstUserMessage = state.messages
        .where((m) => m.isUser)
        .firstOrNull;
    
    if (firstUserMessage != null) {
      final content = firstUserMessage.content;
      if (content.length > 30) {
        return '${content.substring(0, 30)}...';
      }
      return content;
    }
    
    return 'Chat Session';
  }

  Future<void> _generateResponse(String userInput) async {
    if (_geminiModel == null) {
      await _addErrorMessage('AI assistant is not available right now. Please try again later.');
      return;
    }

    state = state.copyWith(isLoading: true);

    try {
      // Build conversation history for context
      final conversationHistory = _buildConversationHistory();
      
      // Generate response
      final response = await _geminiModel!.generateContent([
        Content.text('$conversationHistory\n\nUser: $userInput\n\nEcoBot:'),
      ]);

      final responseText = response.text?.trim() ?? 'I apologize, but I couldn\'t generate a response. Please try again.';

      final botMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: responseText,
        isUser: false,
        timestamp: DateTime.now(),
        messageType: MessageType.text,
        suggestions: _generateSuggestions(userInput, responseText),
      );

      await _addMessageToState(botMessage);
    } catch (e) {
      if (kDebugMode) {
        print('Error generating AI response: $e');
      }
      await _addErrorMessage('Sorry, I encountered an error. Please try again.');
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  String _buildConversationHistory() {
    final recentMessages = state.messages.take(10).toList();
    return recentMessages
        .map((m) => '${m.isUser ? "User" : "EcoBot"}: ${m.content}')
        .join('\n');
  }

  List<String> _generateSuggestions(String userInput, String response) {
    // Generate contextual suggestions based on the conversation
    final suggestions = <String>[];
    
    if (userInput.toLowerCase().contains('upcycl') || userInput.toLowerCase().contains('diy')) {
      suggestions.addAll([
        'Show me more upcycling ideas',
        'What materials do I need?',
        'Find similar products',
      ]);
    } else if (userInput.toLowerCase().contains('product') || userInput.toLowerCase().contains('buy')) {
      suggestions.addAll([
        'Browse marketplace',
        'Compare prices',
        'Check reviews',
      ]);
    } else {
      suggestions.addAll([
        'Tell me more',
        'Show examples',
        'What else can I do?',
      ]);
    }
    
    return suggestions.take(3).toList();
  }

  Future<void> _addErrorMessage(String error) async {
    final errorMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: error,
      isUser: false,
      timestamp: DateTime.now(),
      messageType: MessageType.error,
    );

    await _addMessageToState(errorMessage);
  }

  void clearChat() {
    state = state.copyWith(messages: []);
    _createNewSession();
  }

  void markAsRead() {
    state = state.copyWith(unreadCount: 0);
  }
}
