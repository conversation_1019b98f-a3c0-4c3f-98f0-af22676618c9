import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/product_model.dart';
import '../../../../shared/providers/product_provider.dart';
import '../screens/product_detail_screen.dart';

class ProductSearchDelegate extends SearchDelegate<ProductModel?> {
  final WidgetRef ref;

  ProductSearchDelegate(this.ref);

  @override
  String get searchFieldLabel => 'Search products...';

  @override
  ThemeData appBarTheme(BuildContext context) {
    return Theme.of(context).copyWith(
      appBarTheme: const AppBarTheme(
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      inputDecorationTheme: const InputDecorationTheme(
        border: InputBorder.none,
        hintStyle: TextStyle(color: Colors.white70),
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          showSuggestions(context);
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    if (query.isEmpty) {
      return const Center(
        child: Text('Enter a search term'),
      );
    }

    return Consumer(
      builder: (context, ref, child) {
        final searchNotifier = ref.read(productSearchProvider.notifier);
        
        // Trigger search
        WidgetsBinding.instance.addPostFrameCallback((_) {
          searchNotifier.searchProducts(query);
        });

        final searchResults = ref.watch(productSearchProvider);

        return searchResults.when(
          data: (products) {
            if (products.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.search_off,
                      size: 64,
                      color: AppTheme.textSecondary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No products found for "$query"',
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Try different keywords or browse categories',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              itemCount: products.length,
              itemBuilder: (context, index) {
                final product = products[index];
                return _buildProductTile(context, product);
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppTheme.errorColor,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error searching products',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppTheme.errorColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  error.toString(),
                  style: AppTextStyles.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return _buildSearchSuggestions(context);
    }

    return buildResults(context);
  }

  Widget _buildSearchSuggestions(BuildContext context) {
    final suggestions = [
      'Upcycled furniture',
      'Eco-friendly bags',
      'Recycled decorations',
      'Handmade crafts',
      'Sustainable clothing',
      'Garden planters',
      'Bird feeders',
      'Storage solutions',
    ];

    return ListView.builder(
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = suggestions[index];
        return ListTile(
          leading: const Icon(
            Icons.search,
            color: AppTheme.primaryGreen,
          ),
          title: Text(suggestion),
          onTap: () {
            query = suggestion;
            showResults(context);
          },
        );
      },
    );
  }

  Widget _buildProductTile(BuildContext context, ProductModel product) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ),
      leading: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: AppTheme.lightGreen.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: product.imageUrls.isNotEmpty
            ? ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.asset(
                  product.imageUrls.first,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(
                      Icons.image,
                      color: AppTheme.primaryGreen,
                    );
                  },
                ),
              )
            : const Icon(
                Icons.image,
                color: AppTheme.primaryGreen,
              ),
      ),
      title: Text(
        product.name,
        style: AppTextStyles.bodyLarge.copyWith(
          fontWeight: FontWeight.w600,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            product.description,
            style: AppTextStyles.bodyMedium,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                '₹${product.price.toInt()}',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppTheme.primaryGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 8),
              if (product.rating > 0) ...[
                const Icon(
                  Icons.star,
                  size: 16,
                  color: Colors.amber,
                ),
                const SizedBox(width: 4),
                Text(
                  product.rating.toStringAsFixed(1),
                  style: AppTextStyles.caption,
                ),
              ],
            ],
          ),
        ],
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppTheme.textSecondary,
      ),
      onTap: () {
        close(context, product);
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ProductDetailScreen(productId: product.id),
          ),
        );
      },
    );
  }
}

// Enhanced search provider with filters
final productSearchProvider = StateNotifierProvider<ProductSearchNotifier, AsyncValue<List<ProductModel>>>((ref) {
  return ProductSearchNotifier();
});

class ProductSearchNotifier extends StateNotifier<AsyncValue<List<ProductModel>>> {
  ProductSearchNotifier() : super(const AsyncValue.data([]));

  Future<void> searchProducts(String query, {
    String? category,
    double? minPrice,
    double? maxPrice,
    double? minRating,
  }) async {
    if (query.isEmpty) {
      state = const AsyncValue.data([]);
      return;
    }

    state = const AsyncValue.loading();
    
    try {
      // In a real app, this would be a Firestore query with proper indexing
      // For now, we'll use the sample products and filter them
      final sampleProducts = ProductService.getSampleProducts();
      
      final filteredProducts = sampleProducts.where((product) {
        // Text search
        final matchesQuery = product.name.toLowerCase().contains(query.toLowerCase()) ||
                           product.description.toLowerCase().contains(query.toLowerCase()) ||
                           product.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
        
        if (!matchesQuery) return false;
        
        // Category filter
        if (category != null && product.category != category) return false;
        
        // Price filters
        if (minPrice != null && product.price < minPrice) return false;
        if (maxPrice != null && product.price > maxPrice) return false;
        
        // Rating filter
        if (minRating != null && product.rating < minRating) return false;
        
        return true;
      }).toList();

      state = AsyncValue.data(filteredProducts);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  void clearSearch() {
    state = const AsyncValue.data([]);
  }
}
